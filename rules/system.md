# System Architecture & Design Patterns (`rules/system.md`)

**This document is the canonical source for the project's system architecture, design patterns, and technical guidelines. All development work must align with the principles and structures outlined herein. Deviations require explicit approval and documentation.**

## 1. Overall Architecture Philosophy

TaraDental follows a **Layered Modular Monolith** architecture designed for rapid development, maintainability, and future scalability. The core philosophy emphasizes:

- **Separation of Concerns**: Clear boundaries between presentation, business logic, and data layers
- **Feature-Based Organization**: Code organized by business functionality (lessons, roleplay, chat, analytics)
- **Type Safety**: TypeScript throughout the entire stack for enhanced developer experience
- **Simplicity First**: Avoiding over-engineering while maintaining professional standards
- **AI-First Design**: Architecture optimized for AI service integration and real-time interactions

```mermaid
graph TD
    User[User] -- HTTPS --> Frontend[React Frontend]
    Frontend -- REST API --> Backend[Node.js Backend]
    Backend -- SQL --> Database[(Supabase PostgreSQL)]
    Backend -- API Calls --> OpenAI[OpenAI API]
    Backend -- API Calls --> ElevenLabs[ElevenLabs API]
    Frontend -- Real-time --> Database
    Backend -- Auth --> Database
```

## 2. Backend System Patterns

### 2.1. Core Backend Architecture

The backend follows a **Layered Architecture** with clear separation of responsibilities:

- **Routes Layer**: Express.js route handlers for HTTP request/response
- **Controllers Layer**: Request validation and response formatting
- **Services Layer**: Business logic and orchestration
- **Models Layer**: Data structures and type definitions
- **Data Access Layer**: Supabase client and database operations

```mermaid
graph TD
    Routes[Routes] --> Controllers[Controllers]
    Controllers --> Services[Services]
    Services --> Models[Models]
    Services --> External[External APIs]
    Models --> Database[(Database)]
```

### 2.2. Key Backend Design Patterns

**Repository Pattern**
- **Purpose**: Abstracts data access logic behind consistent interfaces
- **Implementation**: Each entity (User, Lesson, RoleplaySession) has a dedicated service class
- **Benefits**: Enables easy testing with mocks and potential database migrations

**Factory Pattern**
- **Purpose**: Creates AI service instances without exposing instantiation logic
- **Implementation**: `aiService.ts` factory creates OpenAI or ElevenLabs clients based on request type
- **Benefits**: Simplifies AI provider management and enables easy switching

**Middleware Pattern**
- **Purpose**: Handles cross-cutting concerns in the request pipeline
- **Implementation**: Authentication, validation, rate limiting, and error handling middleware
- **Benefits**: Consistent application of security and validation rules

### 2.3. Backend Component Relationships & Flows

**Authentication Flow:**
```
Client Request → Auth Middleware → JWT Validation → Supabase Auth → Route Handler
```

**Lesson Completion Flow:**
```
POST /lessons/:id/complete → lessonController → lessonService → progressService → Database
```

**AI Roleplay Flow:**
```
POST /roleplay/interact → roleplayController → aiService → OpenAI/ElevenLabs → Response Processing
```

### 2.4. Backend Module/Directory Structure

```
backend/src/
├── controllers/          # HTTP request handlers
├── services/            # Business logic layer
├── models/              # Data models and types
├── middleware/          # Express middleware functions
├── routes/              # API route definitions
├── utils/               # Utility functions
├── config/              # Configuration management
├── app.ts               # Express application setup
└── server.ts            # Server entry point
```

## 3. Frontend System Patterns

### 3.1. Core Frontend Architecture

The frontend follows a **Component-Based SPA** architecture using React with TypeScript:

- **Pages**: Top-level route components
- **Components**: Reusable UI components organized by feature
- **Hooks**: Custom React hooks for state and side effects
- **Services**: API communication and external service integration
- **Store**: Global state management using Zustand

```mermaid
graph TD
    Pages[Pages] --> Components[Components]
    Components --> Hooks[Custom Hooks]
    Hooks --> Services[Services]
    Services --> API[Backend API]
    Components --> Store[Zustand Store]
```

### 3.2. Key Frontend Technical Decisions & Patterns

**State Management**: Zustand for global state
- **Rationale**: Simpler than Redux, excellent TypeScript support
- **Pattern**: Feature-based stores (authStore, lessonStore, progressStore)

**Component Design**: Feature-based organization
- **Pattern**: Components grouped by business functionality
- **Structure**: `components/lessons/`, `components/roleplay/`, `components/chat/`

**API Communication**: Custom hooks with service layer
- **Pattern**: `useAuth`, `useAI`, `useProgress` hooks
- **Benefits**: Reusable logic, consistent error handling

### 3.3. Frontend Component Relationships & Structure

```
frontend/src/
├── components/          # Feature-based component organization
│   ├── common/         # Shared UI components
│   ├── auth/           # Authentication components
│   ├── lessons/        # Daily lessons components
│   ├── roleplay/       # AI roleplay components
│   ├── chat/           # Chat with Tara components
│   └── analytics/      # Performance tracking components
├── pages/              # Route-level components
├── hooks/              # Custom React hooks
├── services/           # API and external service calls
├── types/              # TypeScript type definitions
├── utils/              # Utility functions
└── store/              # Zustand state stores
```

### 3.4. Critical Frontend Implementation Paths/Flows

**User Authentication Flow:**
```
LoginForm → useAuth hook → authService → Supabase Auth → authStore update → Route protection
```

**Lesson Interaction Flow:**
```
LessonCard → lessonStore → lessonService → API call → Progress update → UI refresh
```

**AI Roleplay Flow:**
```
PatientSimulator → useAI hook → aiService → Backend API → Real-time response → Feedback display
```

## 4. Cross-Cutting Concerns & Platform-Wide Patterns

**Error Handling:**
- Backend: Centralized error middleware with structured error responses
- Frontend: Error boundaries and consistent error state management

**Logging:**
- Backend: Structured logging with request correlation IDs
- Frontend: Error tracking and user interaction analytics

**Validation:**
- Backend: Joi/Zod schema validation in middleware
- Frontend: Form validation with react-hook-form and Zod schemas
- Shared: Common validation schemas in `/shared` directory

**Security:**
- Authentication: Supabase Auth with JWT tokens
- Authorization: Role-based access control middleware
- API Security: Rate limiting, CORS, and input sanitization
- Data Protection: HIPAA-compliant data handling practices

**Configuration Management:**
- Environment-based configuration with `.env` files
- Type-safe configuration objects
- Separate configs for development, staging, and production

**API Design Principles:**
- RESTful resource-based URLs
- Consistent response formats with status codes
- API versioning strategy (`/api/`)
- Comprehensive OpenAPI documentation

**Testing Strategy:**
- Unit Tests: Jest for both frontend and backend
- Integration Tests: Supertest for API endpoints
- E2E Tests: Playwright for critical user flows
- Component Tests: React Testing Library

## 5. Key Technology Stack Summary

**Backend:**
- Node.js 18+ with TypeScript
- Express.js for REST API
- Supabase (PostgreSQL) for database and authentication
- OpenAI API for natural language processing
- ElevenLabs API for voice synthesis
- Jest for testing

**Frontend:**
- React 18+ with TypeScript
- Vite for build tooling
- Zustand for state management
- Tailwind CSS for styling
- React Router for navigation
- React Hook Form for form handling

**Shared Tools:**
- TypeScript for type safety
- ESLint and Prettier for code quality
- Vercel for deployment
- Git for version control
- Docker for local development environment

---
*This document should be reviewed and updated as the system evolves.*
