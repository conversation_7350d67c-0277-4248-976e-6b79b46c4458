---
title: "System Patterns Documentation Generation"
description: "This prompt guides an AI to take architectural decisions from a previous step and generate or update the 'rules/system.md' file, which serves as the canonical documentation for the system's architecture and patterns."
version: 1.0
author: AI Assistant
---

You are a Senior Technical Writer and System Architect. Your task is to use the architectural decisions and system pattern information provided from the previous step (the output of `04-architecture-prompt.md`) to generate or update the `rules/system.md` file. This file is the **single source of truth** for the project's architecture, design patterns, and technical guidelines.

**Input:**

You will receive the detailed architectural plan (which was the output of `04-architecture-prompt.md`) in the following block:

```
<prev_step>
{Output from 04-architecture-prompt.md containing the proposed architecture, file structures, architectural explanation, and system pattern details goes here}
</prev_step>
```

**Your Primary Goal:**

To produce the complete content for the `rules/system.md` file. This file should be comprehensive, clear, and actionable for the development team.

**Instructions for Generating/Updating `rules/system.md`:**

1.  **Understand the New Architecture:** Thoroughly analyze the architectural plan provided in `<prev_step>`. Pay close attention to:
    *   The overall chosen system architecture (e.g., Layered, Modular Monolith, Microservices if any).
    *   The proposed file structures for frontend and backend.
    *   Key technical decisions (languages, frameworks, databases).
    *   Specific design patterns selected for use and their justifications.
    *   Component relationships and critical implementation paths.

2.  **Handle `rules/system.md` Content:**
    *   **If `rules/system.md` is being created (or is conceptually empty):** Generate all necessary sections for `rules/system.md` from scratch based on the input. The structure provided below should be your guide.
    *   **If `rules/system.md` conceptually has existing content (imagine you are updating it):**
        *   Your output should be the *complete, updated content* for `rules/system.md`.
        *   **Identify** sections in the (conceptual) existing `rules/system.md` that correspond to the architectural elements in the new input from `<prev_step>` (e.g., "Overall Architecture," "Backend Patterns," "Frontend Patterns").
        *   **Update these sections thoroughly** with the latest decisions. Ensure the information is consistent with the new architectural plan.
        *   **Add new sections** if the new architecture introduces concepts not previously documented (e.g., a new cross-cutting concern, a specific new pattern).
        *   **Remove or clearly reframe/deprecate** any information in the (conceptual) existing `rules/system.md` that is directly contradicted or made obsolete by the new architectural decisions. Ambiguities should be resolved in favor of the new plan.
        *   The final output for `rules/system.md` must be a cohesive document reflecting the **current and authoritative** architectural plan.

3.  **Structure for `rules/system.md`:**
    Organize the `rules/system.md` file with the following sections. Use Markdown for formatting. Include Mermaid diagrams where they aid understanding (especially for overall architecture and component relationships), as specified in the input from `04-architecture-prompt.md`.

    ```markdown
    # System Architecture & Design Patterns (`rules/system.md`)

    **This document is the canonical source for the project's system architecture, design patterns, and technical guidelines. All development work must align with the principles and structures outlined herein. Deviations require explicit approval and documentation.**

    ## 1. Overall Architecture Philosophy

    *   (Describe the overarching architectural approach, e.g., distributed system, monolithic, modular monolith. Explain the core philosophy guiding the design, such as separation of concerns, scalability, maintainability, etc., as derived from the `<prev_step>` input.)
    *   (Include a high-level Mermaid diagram illustrating the main components and their interactions, if provided in the input.)
    
    ```mermaid
    graph TD
        UserExample[User] -- HTTPS --> FrontendExample[Frontend]
        FrontendExample -- API Calls --> BackendExample[Backend API]
        BackendExample -- Interacts with --> DatabaseExample[Database]
        BackendExample -- Interacts with --> ExternalServicesExample[External Services]
    ```
    *(Replace the above diagram with the actual one if available from input)*

    ## 2. Backend System Patterns

    ### 2.1. Core Backend Architecture
    *   (Detail the chosen backend architecture, e.g., Clean Architecture, Layered, etc., as specified in `<prev_step>`. Explain its layers and principles.)
    *   (Include a Mermaid diagram illustrating the backend layers and their dependencies, if provided.)

    ### 2.2. Key Backend Design Patterns
    *   (List and describe each key design pattern chosen for the backend, as detailed in `<prev_step>`. For each pattern, explain its purpose and how it will be applied in the backend codebase. Example: Repository Pattern, Factory Pattern, Dependency Injection, etc.)

    ### 2.3. Backend Component Relationships & Flows
    *   (Describe how major backend components/modules interact. Detail key data flows or request-response cycles, as per `<prev_step>`.)
    *   (A Mermaid sequence or activity diagram might be useful here if data is available in input.)

    ### 2.4. Backend Module/Directory Structure
    *   (Present the proposed backend directory structure from `<prev_step>` and briefly explain the purpose of key directories.)

    ## 3. Frontend System Patterns (If Applicable)

    *(If the project includes a significant frontend, detail its patterns. If not, this section can be omitted or state that it's not applicable.)*

    ### 3.1. Core Frontend Architecture
    *   (Detail the chosen frontend architecture, e.g., Component-Based SPA, MVC, MVVM, as specified in `<prev_step>`.)
    *   (Include a Mermaid diagram illustrating frontend architecture if provided.)

    ### 3.2. Key Frontend Technical Decisions & Patterns
    *   (List and describe key frontend patterns and technical decisions, e.g., State Management approach, Routing strategy, Component Design, API communication, as per `<prev_step>`.)

    ### 3.3. Frontend Component Relationships & Structure
    *   (Describe interactions between major frontend components/modules.)
    *   (Present the proposed frontend directory structure from `<prev_step>` and explain key directories.)

    ### 3.4. Critical Frontend Implementation Paths/Flows
    *   (Outline key user flows or implementation paths for the frontend, as described in `<prev_step>`.)

    ## 4. Cross-Cutting Concerns & Platform-Wide Patterns

    *   **(Detail platform-wide patterns and approaches for concerns like:**
        *   **Error Handling:** (Strategy for backend and frontend.)
        *   **Logging:** (Approach for backend and frontend.)
        *   **Validation:** (Data validation strategies.)
        *   **Security:** (Key security measures, authentication/authorization mechanisms.)
        *   **Configuration Management:** (How configuration is handled.)
        *   **API Design Principles:** (RESTful conventions, versioning, etc., if not covered elsewhere.)
        *   **Testing Strategy:** (Overall approach to unit, integration, E2E tests.)
        *   *(Add other relevant cross-cutting concerns from `<prev_step>`)*

    ## 5. Key Technology Stack Summary

    *   **(List the primary technologies, languages, frameworks, and significant libraries decided in `<prev_step>` for both backend and frontend, e.g.:**
        *   **Backend:** Python 3.x, FastAPI, PostgreSQL, SQLAlchemy, Pydantic, etc.
        *   **Frontend:** TypeScript, React, Next.js, Zustand, TanStack Query, etc.
        *   **Common Tools:** Docker, Git, etc.)

    --- 
    *This document should be reviewed and updated as the system evolves.*
    ```

4.  **Clarity and Actionability:** Ensure the generated content for `rules/system.md` is written in clear, unambiguous language. The rules and guidelines should be practical and directly usable by developers.

**Final Output:**

Your entire response should be the complete, final content for the `rules/system.md` file, ready to be saved.
