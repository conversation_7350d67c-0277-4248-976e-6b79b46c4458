---
title: "System Patterns Documentation Generation"
description: "This prompt guides an AI to take architectural decisions from a previous step and generate or update the 'rules/system.md' file, which serves as the canonical documentation for the system's architecture and patterns."
version: 1.0
author: AI Assistant
---

You are a Senior Technical Writer and System Architect. Your task is to use the architectural decisions and system pattern information provided from the previous step (the output of `04-architecture-prompt.md`) to generate or update the `rules/system.md` file. This file is the **single source of truth** for the project's architecture, design patterns, and technical guidelines.

**Input:**

You will receive the detailed architectural plan (which was the output of `04-architecture-prompt.md`) in the following block:

```
<prev_step>
# TaraDental - AI Dental Sales Training Platform
## Software Architecture Document

## 1. Current File Structure

```
.
├── docs/
│   ├── taradental_prd.md
│   └── td_pitch.md
└── instructions/
    ├── 1-idea.instructions.md
    ├── 2-prd.instructions.md
    ├── 3-prd-plus.instructions.md
    ├── 4-architecture.instructions.md
    ├── 5-systems-patterns.instructions.md
    ├── 6-tasks.instructions.md
    ├── 7-tasks-plus.instructions.md
    └── 8-tests.instructions.md
```

*Current structure shows only documentation and instruction files. No application code exists yet.*

## 2. Proposed File Structure

```
taradental/
├── frontend/                          # React TypeScript application
│   ├── public/
│   │   ├── index.html
│   │   └── favicon.ico
│   ├── src/
│   │   ├── components/                 # Reusable UI components
│   │   │   ├── common/                 # Generic components
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   └── LoadingSpinner.tsx
│   │   │   ├── auth/                   # Authentication components
│   │   │   │   ├── LoginForm.tsx
│   │   │   │   └── SignupForm.tsx
│   │   │   ├── lessons/                # Daily lessons components
│   │   │   │   ├── LessonCard.tsx
│   │   │   │   ├── QuizComponent.tsx
│   │   │   │   └── ChallengeForm.tsx
│   │   │   ├── roleplay/               # AI roleplay components
│   │   │   │   ├── PatientSimulator.tsx
│   │   │   │   ├── VoiceRecorder.tsx
│   │   │   │   └── FeedbackDisplay.tsx
│   │   │   ├── chat/                   # Chat with Tara components
│   │   │   │   ├── ChatInterface.tsx
│   │   │   │   └── MessageBubble.tsx
│   │   │   └── analytics/              # Performance tracking
│   │   │       ├── ProgressChart.tsx
│   │   │       └── ScoreDisplay.tsx
│   │   ├── pages/                      # Page-level components
│   │   │   ├── Dashboard.tsx
│   │   │   ├── Lessons.tsx
│   │   │   ├── Roleplay.tsx
│   │   │   ├── Chat.tsx
│   │   │   ├── Analytics.tsx
│   │   │   └── Profile.tsx
│   │   ├── hooks/                      # Custom React hooks
│   │   │   ├── useAuth.ts
│   │   │   ├── useSupabase.ts
│   │   │   ├── useAI.ts
│   │   │   └── useProgress.ts
│   │   ├── services/                   # API and external service calls
│   │   │   ├── api.ts
│   │   │   ├── supabase.ts
│   │   │   ├── openai.ts
│   │   │   └── elevenlabs.ts
│   │   ├── types/                      # TypeScript type definitions
│   │   │   ├── user.ts
│   │   │   ├── lesson.ts
│   │   │   ├── roleplay.ts
│   │   │   └── api.ts
│   │   ├── utils/                      # Utility functions
│   │   │   ├── constants.ts
│   │   │   ├── helpers.ts
│   │   │   └── validation.ts
│   │   ├── store/                      # State management (Zustand)
│   │   │   ├── authStore.ts
│   │   │   ├── lessonStore.ts
│   │   │   └── progressStore.ts
│   │   ├── App.tsx
│   │   ├── index.tsx
│   │   └── index.css
│   ├── package.json
│   ├── tsconfig.json
│   ├── tailwind.config.js
│   └── vite.config.ts
├── backend/                            # Node.js TypeScript API
│   ├── src/
│   │   ├── controllers/                # Request handlers
│   │   │   ├── authController.ts
│   │   │   ├── lessonController.ts
│   │   │   ├── roleplayController.ts
│   │   │   ├── chatController.ts
│   │   │   └── analyticsController.ts
│   │   ├── services/                   # Business logic layer
│   │   │   ├── authService.ts
│   │   │   ├── lessonService.ts
│   │   │   ├── roleplayService.ts
│   │   │   ├── aiService.ts
│   │   │   └── progressService.ts
│   │   ├── models/                     # Data models and types
│   │   │   ├── User.ts
│   │   │   ├── Lesson.ts
│   │   │   ├── RoleplaySession.ts
│   │   │   └── Progress.ts
│   │   ├── middleware/                 # Express middleware
│   │   │   ├── auth.ts
│   │   │   ├── validation.ts
│   │   │   ├── rateLimit.ts
│   │   │   └── errorHandler.ts
│   │   ├── routes/                     # API route definitions
│   │   │   ├── auth.ts
│   │   │   ├── lessons.ts
│   │   │   ├── roleplay.ts
│   │   │   ├── chat.ts
│   │   │   └── analytics.ts
│   │   ├── utils/                      # Utility functions
│   │   │   ├── database.ts
│   │   │   ├── validation.ts
│   │   │   └── constants.ts
│   │   ├── config/                     # Configuration files
│   │   │   ├── database.ts
│   │   │   ├── openai.ts
│   │   │   └── elevenlabs.ts
│   │   ├── app.ts                      # Express app setup
│   │   └── server.ts                   # Server entry point
│   ├── package.json
│   ├── tsconfig.json
│   └── .env.example
├── database/                           # Database schema and migrations
│   ├── schema.sql                      # Complete database schema
│   ├── migrations/                     # Database migration files
│   │   ├── 001_initial_schema.sql
│   │   ├── 002_add_personas.sql
│   │   └── 003_add_analytics.sql
│   └── seed/                          # Seed data for development
│       ├── lessons.sql
│       ├── personas.sql
│       └── scenarios.sql
├── shared/                            # Shared types and utilities
│   ├── types/                         # Common TypeScript types
│   │   ├── api.ts
│   │   ├── user.ts
│   │   └── lesson.ts
│   └── utils/                         # Shared utility functions
│       ├── validation.ts
│       └── constants.ts
├── tests/                             # Test files
│   ├── frontend/                      # Frontend tests
│   │   ├── components/
│   │   └── pages/
│   ├── backend/                       # Backend tests
│   │   ├── controllers/
│   │   ├── services/
│   │   └── integration/
│   └── e2e/                          # End-to-end tests
│       ├── auth.spec.ts
│       ├── lessons.spec.ts
│       └── roleplay.spec.ts
├── docs/                              # Documentation
│   ├── api/                          # API documentation
│   │   └── openapi.yaml
│   ├── deployment/                    # Deployment guides
│   │   └── vercel.md
│   └── development/                   # Development guides
│       └── setup.md
├── .gitignore
├── README.md
├── package.json                       # Root package.json for workspace
├── docker-compose.yml                 # Local development setup
└── vercel.json                        # Vercel deployment config
```

## 3. Architectural Explanation

The proposed architecture follows a **Modular Monolith** approach with clear separation between frontend and backend, optimized for rapid development and deployment on Vercel. This structure addresses the current state (documentation-only) by providing a complete, production-ready foundation.

### Key Benefits of This Structure:

1. **Clear Separation of Concerns**: Frontend, backend, database, and shared code are distinctly organized
2. **Feature-Based Organization**: Components and services are grouped by functionality (lessons, roleplay, chat, analytics)
3. **Scalability**: Modular structure allows for easy feature additions and potential microservice extraction
4. **Developer Experience**: TypeScript throughout, clear naming conventions, and logical file organization
5. **Testing Strategy**: Comprehensive test structure covering unit, integration, and e2e testing

### Alignment with PRD Requirements:

- **React/TypeScript Frontend**: Matches technical requirements for modern web application
- **Node.js/TypeScript Backend**: Provides type safety and rapid development
- **Supabase Integration**: Dedicated database folder with schema and migrations
- **AI Service Integration**: Separate service files for OpenAI and ElevenLabs APIs
- **Modular Components**: Each major feature (lessons, roleplay, chat) has dedicated component folders

## 4. System Patterns

### A. System Architecture

**Chosen Architecture: Layered Modular Monolith**

This architecture provides:
- **Presentation Layer**: React frontend with component-based UI
- **API Layer**: Express.js REST API with clear route organization
- **Business Logic Layer**: Service classes handling core functionality
- **Data Access Layer**: Supabase client with typed models
- **External Integration Layer**: Dedicated services for AI APIs

**Justification**: For a startup product like TaraDental, this architecture offers the perfect balance of simplicity, rapid development, and future scalability. It avoids the complexity of microservices while maintaining clear boundaries between concerns.

### B. Key Technical Decisions

1. **Frontend Framework**: React with TypeScript
   - **Rationale**: Mature ecosystem, excellent TypeScript support, large talent pool
   - **State Management**: Zustand for simplicity over Redux complexity
   - **Styling**: Tailwind CSS for rapid UI development

2. **Backend Framework**: Node.js with Express and TypeScript
   - **Rationale**: JavaScript/TypeScript consistency across stack, excellent async support for AI API calls
   - **API Design**: RESTful endpoints with clear resource-based routing

3. **Database**: Supabase (PostgreSQL)
   - **Rationale**: Built-in authentication, real-time features, excellent TypeScript support
   - **Schema**: Comprehensive design supporting all PRD features

4. **AI Integration**:
   - **OpenAI API**: For chat functionality and natural language processing
   - **ElevenLabs API**: For voice synthesis in roleplay scenarios
   - **Rationale**: Best-in-class services for specific AI needs

5. **Deployment**: Vercel
   - **Rationale**: Seamless Next.js/React deployment, excellent performance, simple CI/CD

### C. Design Patterns in Use

1. **Repository Pattern**
   - **Description**: Abstracts data access logic behind a consistent interface
   - **Application**: Each model (User, Lesson, RoleplaySession) has a corresponding service class that handles all database operations
   - **Benefit**: Enables easy testing with mock repositories and potential database changes

2. **Factory Pattern**
   - **Description**: Creates objects without exposing instantiation logic
   - **Application**: AI service factory creates appropriate AI clients (OpenAI, ElevenLabs) based on request type
   - **Benefit**: Simplifies AI service management and enables easy switching between providers

3. **Middleware Pattern (Express.js)**
   - **Description**: Functions that execute during request-response cycle
   - **Application**: Authentication, validation, rate limiting, and error handling middleware
   - **Benefit**: Cross-cutting concerns handled consistently across all routes

### D. Component Relationships

The system follows a clear hierarchical flow:

**Frontend Flow:**
```
Pages → Components → Hooks → Services → API
```

**Backend Flow:**
```
Routes → Controllers → Services → Models → Database
```

**Key Relationships:**

1. **Authentication Flow**:
   - `LoginForm` → `useAuth` hook → `authService` → `Supabase Auth`
   - Middleware validates tokens on protected routes

2. **Lesson System**:
   - `LessonCard` → `lessonStore` → `lessonService` → `lessonController` → Database
   - XP tracking flows through `progressService`

3. **AI Roleplay**:
   - `PatientSimulator` → `useAI` hook → `aiService` → `OpenAI/ElevenLabs APIs`
   - Feedback stored via `roleplayService`

4. **Real-time Features**:
   - Supabase real-time subscriptions for progress updates
   - WebSocket connections for live chat features

### E. Critical Implementation Paths

**Phase 1: Foundation (Weeks 1-4)**
1. Setup project structure with TypeScript configurations
2. Implement Supabase database schema and authentication
3. Create basic React app with routing and authentication flow
4. Develop core API endpoints for user management
5. Implement basic lesson CRUD operations

**Phase 2: Core Features (Weeks 5-8)**
1. Build lesson system with XP tracking and gamification
2. Integrate OpenAI API for chat functionality
3. Develop roleplay system with patient personas
4. Implement LAARC framework coaching logic
5. Create performance scoring and feedback systems

**Phase 3: AI Integration (Weeks 9-12)**
1. Integrate ElevenLabs for voice synthesis
2. Implement voice recording and processing
3. Build advanced analytics and progress tracking
4. Develop treatment plan presentation tools
5. Add real-time features and notifications

**Phase 4: Polish & Launch (Weeks 13-16)**
1. Comprehensive testing (unit, integration, e2e)
2. Performance optimization and caching
3. Security audit and HIPAA compliance review
4. Mobile responsiveness and accessibility
5. Deployment automation and monitoring setup

**Critical Dependencies:**
- Supabase setup must be completed before any data-dependent features
- OpenAI API integration is prerequisite for chat and roleplay features
- Authentication system blocks all user-specific functionality
- Database schema changes require careful migration planning

**Risk Mitigation:**
- Early AI API integration testing to validate response quality
- Incremental feature rollout to validate user engagement
- Performance testing with realistic data volumes
- Security review at each phase boundary
</prev_step>
```

**Your Primary Goal:**

To produce the complete content for the `rules/system.md` file. This file should be comprehensive, clear, and actionable for the development team.

**Instructions for Generating/Updating `rules/system.md`:**

1.  **Understand the New Architecture:** Thoroughly analyze the architectural plan provided in `<prev_step>`. Pay close attention to:
    *   The overall chosen system architecture (e.g., Layered, Modular Monolith, Microservices if any).
    *   The proposed file structures for frontend and backend.
    *   Key technical decisions (languages, frameworks, databases).
    *   Specific design patterns selected for use and their justifications.
    *   Component relationships and critical implementation paths.

2.  **Handle `rules/system.md` Content:**
    *   **If `rules/system.md` is being created (or is conceptually empty):** Generate all necessary sections for `rules/system.md` from scratch based on the input. The structure provided below should be your guide.
    *   **If `rules/system.md` conceptually has existing content (imagine you are updating it):**
        *   Your output should be the *complete, updated content* for `rules/system.md`.
        *   **Identify** sections in the (conceptual) existing `rules/system.md` that correspond to the architectural elements in the new input from `<prev_step>` (e.g., "Overall Architecture," "Backend Patterns," "Frontend Patterns").
        *   **Update these sections thoroughly** with the latest decisions. Ensure the information is consistent with the new architectural plan.
        *   **Add new sections** if the new architecture introduces concepts not previously documented (e.g., a new cross-cutting concern, a specific new pattern).
        *   **Remove or clearly reframe/deprecate** any information in the (conceptual) existing `rules/system.md` that is directly contradicted or made obsolete by the new architectural decisions. Ambiguities should be resolved in favor of the new plan.
        *   The final output for `rules/system.md` must be a cohesive document reflecting the **current and authoritative** architectural plan.

3.  **Structure for `rules/system.md`:**
    Organize the `rules/system.md` file with the following sections. Use Markdown for formatting. Include Mermaid diagrams where they aid understanding (especially for overall architecture and component relationships), as specified in the input from `04-architecture-prompt.md`.

    ```markdown
    # System Architecture & Design Patterns (`rules/system.md`)

    **This document is the canonical source for the project's system architecture, design patterns, and technical guidelines. All development work must align with the principles and structures outlined herein. Deviations require explicit approval and documentation.**

    ## 1. Overall Architecture Philosophy

    *   (Describe the overarching architectural approach, e.g., distributed system, monolithic, modular monolith. Explain the core philosophy guiding the design, such as separation of concerns, scalability, maintainability, etc., as derived from the `<prev_step>` input.)
    *   (Include a high-level Mermaid diagram illustrating the main components and their interactions, if provided in the input.)
    
    ```mermaid
    graph TD
        UserExample[User] -- HTTPS --> FrontendExample[Frontend]
        FrontendExample -- API Calls --> BackendExample[Backend API]
        BackendExample -- Interacts with --> DatabaseExample[Database]
        BackendExample -- Interacts with --> ExternalServicesExample[External Services]
    ```
    *(Replace the above diagram with the actual one if available from input)*

    ## 2. Backend System Patterns

    ### 2.1. Core Backend Architecture
    *   (Detail the chosen backend architecture, e.g., Clean Architecture, Layered, etc., as specified in `<prev_step>`. Explain its layers and principles.)
    *   (Include a Mermaid diagram illustrating the backend layers and their dependencies, if provided.)

    ### 2.2. Key Backend Design Patterns
    *   (List and describe each key design pattern chosen for the backend, as detailed in `<prev_step>`. For each pattern, explain its purpose and how it will be applied in the backend codebase. Example: Repository Pattern, Factory Pattern, Dependency Injection, etc.)

    ### 2.3. Backend Component Relationships & Flows
    *   (Describe how major backend components/modules interact. Detail key data flows or request-response cycles, as per `<prev_step>`.)
    *   (A Mermaid sequence or activity diagram might be useful here if data is available in input.)

    ### 2.4. Backend Module/Directory Structure
    *   (Present the proposed backend directory structure from `<prev_step>` and briefly explain the purpose of key directories.)

    ## 3. Frontend System Patterns (If Applicable)

    *(If the project includes a significant frontend, detail its patterns. If not, this section can be omitted or state that it's not applicable.)*

    ### 3.1. Core Frontend Architecture
    *   (Detail the chosen frontend architecture, e.g., Component-Based SPA, MVC, MVVM, as specified in `<prev_step>`.)
    *   (Include a Mermaid diagram illustrating frontend architecture if provided.)

    ### 3.2. Key Frontend Technical Decisions & Patterns
    *   (List and describe key frontend patterns and technical decisions, e.g., State Management approach, Routing strategy, Component Design, API communication, as per `<prev_step>`.)

    ### 3.3. Frontend Component Relationships & Structure
    *   (Describe interactions between major frontend components/modules.)
    *   (Present the proposed frontend directory structure from `<prev_step>` and explain key directories.)

    ### 3.4. Critical Frontend Implementation Paths/Flows
    *   (Outline key user flows or implementation paths for the frontend, as described in `<prev_step>`.)

    ## 4. Cross-Cutting Concerns & Platform-Wide Patterns

    *   **(Detail platform-wide patterns and approaches for concerns like:**
        *   **Error Handling:** (Strategy for backend and frontend.)
        *   **Logging:** (Approach for backend and frontend.)
        *   **Validation:** (Data validation strategies.)
        *   **Security:** (Key security measures, authentication/authorization mechanisms.)
        *   **Configuration Management:** (How configuration is handled.)
        *   **API Design Principles:** (RESTful conventions, versioning, etc., if not covered elsewhere.)
        *   **Testing Strategy:** (Overall approach to unit, integration, E2E tests.)
        *   *(Add other relevant cross-cutting concerns from `<prev_step>`)*

    ## 5. Key Technology Stack Summary

    *   **(List the primary technologies, languages, frameworks, and significant libraries decided in `<prev_step>` for both backend and frontend, e.g.:**
        *   **Backend:** Python 3.x, FastAPI, PostgreSQL, SQLAlchemy, Pydantic, etc.
        *   **Frontend:** TypeScript, React, Next.js, Zustand, TanStack Query, etc.
        *   **Common Tools:** Docker, Git, etc.)

    --- 
    *This document should be reviewed and updated as the system evolves.*
    ```

4.  **Clarity and Actionability:** Ensure the generated content for `rules/system.md` is written in clear, unambiguous language. The rules and guidelines should be practical and directly usable by developers.

**Final Output:**

Your entire response should be the complete, final content for the `rules/system.md` file, ready to be saved.
