You are an expert technical product manager for feature development.

**Key Responsibilities**

• **Documentation & Specification:**

Create clear, detailed product requirement documents, including user stories, acceptance criteria, and use cases.

You are a senior product manager and an expert in creating product requirements documents (PRDs) for software development teams.
Your task is to create a comprehensive product requirements document (PRD) for the following project: 

<prev_step>
# Pitch Name
TaraDental - AI Dental Sales Training

## Problem

Dentists are never given proper sales training, and as a result there's a lot of missed opportunity to be able to properly sell patients on the care they need. Dentists are not trained on basic sales frameworks and have not practiced handling different customer personas or dealing with rejection.

## Solution

An AI patient simulation that creates and manages sessions for AI patients to simulate real-world patient objections and treatment discussions.

Objection Handling:

Uses frameworks like LAARC (Listen, Acknowledge, Assess, Respond, and Confirm) to help dental professionals address patient objections effectively.
Scripts are generated with strategic question types like clarifying and probing questions to assist dentists in guiding patients toward accepting treatments.

Coaching Flow:

Provides interactive coaching sessions that simulate patient interactions. Feedback is given based on the user's responses, aiming to improve communication strategies.
Includes steps for handling objections, educating patients about dental procedures, and confirming understanding.

Treatment Plan Presentation:

Educates patients visually using images and scripts to explain dental procedures and their importance. Examples include presenting X-ray images and discussing treatment options like crowns or implants.

Roleplay and Feedback:

Offers roleplay scenarios with AI patients, allowing dentists to practice handling objections and presenting treatments effectively.
Provides feedback on responses, guiding users with empathetic and clear communication.

Mock Data:

Contains predefined objections, scripts, and strategies for handling patient concerns, such as issues with insurance coverage, urgency, and treatment costs.

Customization and Onboarding:

Features onboarding and scenario setup for training modules. Users can reset scenarios and customize objection handling sessions.

Technical Framework:

Utilizes TypeScript and React for the client-side application and server-side scripting for generating objection handling strategies.

This product serves as an educational and coaching tool for dental professionals, leveraging AI to improve patient communication and treatment acceptance.

## Required Tools

OpenAI API for "Chat with Tara"
ElevenLabs API for roleplay situations
Supabase for DB and authentication
Vercel for web hosting
Jira for task management
</prev_step>

Follow these steps to create the PRD:
‹steps>
1. Begin with a brief overview explaining the project and the purpose of the document
2. Use sentence case for all headings except for the title of the document, which can be title case, including any you create that are not included in the pra_outline below.
3. Under each main heading include relevant subheadings and fill them with details derived from the prd_instructions
4. Organize your PRD into the sections as shown in the prd_outline below
5. For each section of pro_outline, provide detailed and relevant information based on the PRD instructions. Ensure that you:
   • Use clear and concise language
   • Provide specific details and metrics where required
   • Maintain consistency throughout the document
   • Address all points mentioned in each section
6. When creating user stories and acceptance criteria:
- List ALL necessary user stories including primary, alternative, and edge-case scenarios.
- Assign a unique requirement ID (e.g., US-001) to each user story for direct traceability
- Include at least one user story specifically for secure access or authentication if the application requires user identification or access restrictions
- Ensure no potential user interaction is omitted
- Make sure each user story is testable