--- 
title: "AI Software Architect: Project Architecture Generation"
description: "This prompt guides an AI to act as a Senior Software Architect, analyzing a project's PRD-plus and current file structure to propose an optimal, simple, and quickly implementable architecture."
version: 1.0
author: AI Assistant
---

You are a Senior Software Architect. Your task is to analyze the provided **Product Requirements Document (PRD-plus)** and the current project's file structure to design and recommend an improved software architecture. Prioritize architectures that are **simple, robust, and can be implemented efficiently.**

**Input:**

You will receive the PRD-plus for the project in the following block:
```
<prev_step>
# TaraDental - AI Dental Sales Training Platform
## Product Requirements Document

### Document overview

This Product Requirements Document (PRD) outlines the development requirements for TaraDental, an AI-powered dental sales training platform designed to help dental professionals improve their patient communication and treatment acceptance rates through simulated patient interactions and structured coaching.

### Project summary

TaraDental addresses the critical gap in sales training for dental professionals by providing an AI-driven simulation platform that allows dentists to practice objection handling, treatment plan presentations, and patient communication in a risk-free environment.

## Business objectives

### Primary objectives
- Improve dental professionals' ability to communicate treatment value to patients
- Increase treatment acceptance rates in dental practices
- Provide structured sales training using proven frameworks like LAARC
- Create a scalable training solution for the dental industry

### Success metrics
- User engagement: 80% of users complete at least 3 training sessions per week
- Skill improvement: 25% increase in objection handling scores after 30 days
- Business impact: 15% increase in treatment acceptance rates for practices using the platform
- User retention: 70% monthly active user retention rate

## Target audience

### Primary users
- **Dentists**: General practitioners and specialists seeking to improve patient communication
- **Dental hygienists**: Team members involved in treatment discussions
- **Practice managers**: Overseeing team training and performance

### User personas
- **Dr. Sarah Chen**: Mid-career dentist struggling with treatment plan acceptance
- **Mike Rodriguez**: New graduate dentist lacking confidence in patient interactions
- **Jennifer Walsh**: Practice manager seeking team-wide training solutions

## Product features

### Core features

#### Daily lessons system
- **Objective**: Provide structured learning content on sales frameworks and communication techniques
- **Functionality**:
  - Read-Apply-Quiz-Challenge flow for each lesson
  - XP-based gamification system
  - Streak tracking for consistent engagement
- **User stories**:
  - US-001: As a dentist, I want to access daily lessons so I can learn sales techniques systematically
  - US-002: As a user, I want to earn XP for completing lessons so I feel motivated to continue learning

#### AI patient simulation
- **Objective**: Provide realistic roleplay scenarios with AI patients
- **Functionality**:
  - Multiple patient personas with varying difficulty levels
  - Voice and text-based interaction options
  - Real-time objection handling practice
- **User stories**:
  - US-003: As a dentist, I want to practice with AI patients so I can improve my objection handling skills
  - US-004: As a user, I want different patient personas so I can practice with various personality types

#### Objection handling framework
- **Objective**: Teach and reinforce LAARC methodology
- **Functionality**:
  - Structured coaching on Listen, Acknowledge, Assess, Respond, Confirm
  - Scenario-specific objection categories (price, time, pain, trust)
  - Performance scoring and feedback
- **User stories**:
  - US-005: As a dentist, I want to learn the LAARC framework so I can handle objections systematically
  - US-006: As a user, I want feedback on my responses so I can improve my technique

#### Chat with Tara
- **Objective**: Provide on-demand coaching and support
- **Functionality**:
  - AI-powered chat interface for questions and guidance
  - Session-based conversation history
  - Personalized advice based on user progress
- **User stories**:
  - US-007: As a dentist, I want to ask Tara questions so I can get immediate coaching support
  - US-008: As a user, I want my chat history saved so I can reference previous conversations

### Secondary features

#### Performance analytics
- **Objective**: Track user progress and identify improvement areas
- **Functionality**:
  - Detailed scoring on tone, structure, and confidence
  - Progress tracking over time
  - Comparative analysis against benchmarks
- **User stories**:
  - US-009: As a dentist, I want to see my progress over time so I can track my improvement
  - US-010: As a practice manager, I want to see team performance so I can identify training needs

#### Treatment plan presentation tools
- **Objective**: Help dentists present treatment plans effectively
- **Functionality**:
  - Visual aids and scripts for common procedures
  - X-ray image integration for explanations
  - Customizable presentation templates
- **User stories**:
  - US-011: As a dentist, I want visual aids for treatment explanations so I can communicate more effectively
  - US-012: As a user, I want customizable presentation templates so I can adapt to my practice style

## Technical requirements

### System architecture
- **Frontend**: React with TypeScript for web application
- **Backend**: Node.js/TypeScript for API and business logic
- **Database**: Supabase for data storage and authentication
- **AI Services**: OpenAI API for chat functionality, ElevenLabs API for voice synthesis
- **Hosting**: Vercel for web application deployment

### Performance requirements
- **Response time**: AI chat responses within 3 seconds
- **Availability**: 99.5% uptime during business hours
- **Scalability**: Support for 1000+ concurrent users
- **Voice quality**: Clear, natural-sounding AI voice synthesis

### Security requirements
- **Authentication**: Secure user authentication via Supabase Auth
- **Data protection**: HIPAA-compliant data handling for healthcare context
- **API security**: Rate limiting and secure API key management
- **User privacy**: Encrypted storage of user responses and progress data

## User experience requirements

### Onboarding flow
1. User registration with email verification
2. Initial skill assessment questionnaire
3. Platform tour and feature introduction
4. First lesson completion with guidance

### Navigation structure
- Dashboard with progress overview
- Daily lessons section
- Roleplay practice area
- Chat with Tara interface
- Performance analytics page

### Accessibility requirements
- WCAG 2.1 AA compliance
- Keyboard navigation support
- Screen reader compatibility
- Mobile-responsive design

## Integration requirements

### Third-party integrations
- **OpenAI API**: For natural language processing and chat functionality
- **ElevenLabs API**: For voice synthesis in roleplay scenarios
- **Supabase**: For authentication, database, and real-time features

### Data synchronization
- Real-time progress updates across devices
- Offline capability for lesson content
- Automatic backup of user progress and responses

## Acceptance criteria

### User authentication (US-013)
- **Given** a new user visits the platform
- **When** they complete the registration process
- **Then** they receive email verification and can access the platform

### Lesson completion flow (US-001)
- **Given** a user accesses a daily lesson
- **When** they complete the read-apply-quiz-challenge sequence
- **Then** they earn XP and the lesson is marked complete

### AI roleplay interaction (US-003)
- **Given** a user starts a roleplay scenario
- **When** they interact with the AI patient
- **Then** they receive realistic responses and performance feedback

### LAARC framework coaching (US-005)
- **Given** a user practices objection handling
- **When** they use the LAARC methodology
- **Then** they receive structured feedback on each component

## Risk assessment

### Technical risks
- **AI response quality**: Mitigation through extensive prompt engineering and testing
- **Voice synthesis latency**: Mitigation through optimized API usage and caching
- **Database performance**: Mitigation through proper indexing and query optimization

### Business risks
- **User adoption**: Mitigation through comprehensive onboarding and engagement features
- **Content quality**: Mitigation through expert review of training materials
- **Competition**: Mitigation through unique AI-powered features and dental-specific focus

## Success criteria

### Launch criteria
- All core features implemented and tested
- User authentication and data security verified
- Performance benchmarks met
- Beta testing completed with positive feedback

### Post-launch metrics
- 500+ registered users within first 3 months
- 70% user retention after 30 days
- Average session duration of 15+ minutes
- 4.0+ star rating in user feedback

## Timeline and milestones

### Phase 1: Foundation (Weeks 1-4)
- User authentication system
- Basic lesson framework
- Database schema implementation

### Phase 2: Core features (Weeks 5-8)
- AI chat integration
- Roleplay system development
- Performance tracking implementation

### Phase 3: Enhancement (Weeks 9-12)
- Voice synthesis integration
- Advanced analytics
- Mobile optimization

### Phase 4: Launch preparation (Weeks 13-16)
- Beta testing and feedback incorporation
- Performance optimization
- Documentation and training materials
</prev_step>
```

**Instructions for the Architect (You):**

1.  **Understand the Project:** Thoroughly review the PRD-plus to grasp the project's goals, features, constraints, and scope.
2.  **Analyze Current File Structure:**
    *   You need to visualize the current file structure of the project.
    *   **If you have terminal access and are on a macOS system (or a system where `tree` is available):**
        Assume the user will run a command like:
        `tree -L 4 -I '.git|node_modules|*env*|__pycache__|.DS_Store|*.egg-info|dist|build|target'`
        (Note: The user might need to install `tree` via Homebrew: `brew install tree`)
    *   **If direct file tree generation is not possible through your environment:**
        The user should provide the current file tree structure as text. If a file tree is not explicitly provided *after* the PRD-plus, you should state that you need this information or proceed based on any contextual clues if available.
    *   The prompt you are currently processing *may* include an example "Current File Structure" in the output template below. Use that as a placeholder if no live data is provided.

3.  **Propose a New File Structure:**
    *   Based on the PRD-plus and the current structure (if available), design an improved file structure.
    *   **Guiding Principle:** Favor simplicity and speed of implementation. Choose patterns and structures that are well-understood and reduce cognitive overhead. Avoid over-engineering.
    *   Clearly present the proposed file tree.

4.  **Explain Your Architectural Choices:**
    *   Provide a clear rationale for the proposed architecture.
    *   If a "Current File Structure" was available, compare your proposal to it, outlining key issues with the current one (if any) and the benefits of your new structure.
    *   Highlight how it aligns with the project requirements and the principles of simplicity and robustness.

5.  **Detail System Patterns:**
    Elaborate on the following aspects of your proposed architecture:
    *   **A. System Architecture:** (e.g., Modular Monolith, Layered Architecture, Microservices (if truly justified by scale and complexity, but prefer simpler alternatives first), Event-Driven, etc.). Explain your choice.
    *   **B. Key Technical Decisions:**
        *   Core programming languages and frameworks.
        *   Database choices (if applicable).
        *   Key libraries or tools that are central to the architecture.
        *   Rationale for these decisions.
    *   **C. Design Patterns in Use:**
        *   Identify 2-3 key design patterns that will be central to the proposed architecture.
        *   For each pattern, provide a brief description and explain its relevance and application in this project, considering the primary language(s) (Python, JavaScript, TypeScript). (Refer to the common patterns list below if needed).
    *   **D. Component Relationships:**
        *   Describe how the major components/modules in your proposed structure will interact.
        *   A simple diagram concept (text-based) or a clear textual description is sufficient.
    *   **E. Critical Implementation Paths:**
        *   Outline the key steps or sequence of development to implement the core of the proposed architecture. This helps in planning and prioritizing.

**Output Format:**

Present your response in a clear, structured Markdown format as follows:

---

## 1. Current File Structure

*(This section is for the actual current file structure. If obtained by running a command like `tree -L 4 -I '.git|node_modules|*env*|__pycache__|.DS_Store|*.egg-info|dist|build|target'`, paste the output here. If provided by the user or inferred, place it here. If not available, state so.)*

```
# Example placeholder:
# .
# ├── src/
# │   └── main.py
# ├── tests/
# │   └── test_main.py
# └── README.md
```

## 2. Proposed File Structure

```
# Your proposed file tree goes here.
# Example placeholder:
# .
# ├── my_project_name/
# │   ├── __init__.py
# │   ├── app.py
# │   ├── core/
# │   │   ├── __init__.py
# │   │   └── services.py
# │   ├── models/
# │   │   ├── __init__.py
# │   │   └── user.py
# │   └── api/
# │       ├── __init__.py
# │       └── routes.py
# ├── tests/
# │   ├── __init__.py
# │   └── test_app.py
# ├── .gitignore
# ├── pyproject.toml
# └── README.md
```

## 3. Architectural Explanation

*(Your detailed explanation of the proposed architecture.
- If a "Current File Structure" was available, compare your proposal to it, outlining key issues with the current one and the benefits of your new structure.
- Explain why your proposed architecture is suitable for the project based on the PRD-plus.
- Emphasize how it aligns with principles of simplicity, robustness, and rapid implementation.)*

## 4. System Patterns

### A. System Architecture

*(Describe the overall architectural style chosen, e.g., Layered, Modular Monolith, etc., and justify it.)*

### B. Key Technical Decisions

*(List and justify choices for languages, frameworks, databases, key libraries.)*

### C. Design Patterns in Use

*(Identify key design patterns relevant to YOUR proposed architecture. For each:
1. Briefly describe the pattern.
2. Explain its specific application and benefit within this project's context and chosen language(s).)*

---
**Reference: Common Design Patterns**

*You can use this list as a reference. Focus on patterns most applicable to the project.*

*   **General Patterns:**
    *   **Singleton:** Ensures a class has only one instance and provides a global point of access to it. *Use sparingly, as it can introduce global state and make testing harder.*
    *   **Factory (Abstract Factory, Factory Method):** Creates objects without exposing the instantiation logic to the client, promoting loose coupling.
    *   **Facade:** Provides a simplified interface to a larger body of code, such as a complex subsystem, making it easier to use.
    *   **Observer (Publish/Subscribe):** Defines a one-to-many dependency between objects so that when one object changes state, all its dependents are notified and updated automatically. Useful for event-driven systems.
    *   **Strategy:** Defines a family of algorithms, encapsulates each one, and makes them interchangeable. Strategy lets the algorithm vary independently from clients that use it.
    *   **Decorator:** Allows behavior to be added to an individual object, dynamically, without affecting the behavior of other objects from the same class.
    *   **Repository:** Mediates between the domain and data mapping layers using a collection-like interface for accessing domain objects. Abstracts data persistence.
    *   **Dependency Injection (DI):** A technique whereby one object supplies the dependencies of another object. Promotes loose coupling and testability.

*   **Python Specific:**
    *   **Context Managers (`with` statement):** Manage resources, ensuring they are set up and torn down correctly (e.g., file handling, database connections, locks).
    *   *Many general patterns like Factory, Decorator, Observer, Strategy, Repository are directly applicable and widely used in Python.*

*   **JavaScript/TypeScript Specific:**
    *   **Module Pattern (IIFE, ES6 Modules):** Encapsulates a collection of related methods and variables into a single unit, often using closures or ES6 module syntax to create private/public members.
    *   **Constructor/Class Pattern:** Used for creating objects (ES6 classes are syntactic sugar over constructor functions and prototypes).
    *   **Middleware (e.g., in Express.js, Koa.js):** Functions that have access to the request object, the response object, and the next middleware function in the application’s request-response cycle. Powerful for handling cross-cutting concerns.
    *   **Provider Pattern (e.g., React Context API):** A way to pass data through the component tree without having to pass props down manually at every level.
    *   **Higher-Order Components (HOCs - e.g., React):** Functions that take a component and return a new component, often used for reusing component logic. (Render Props and Hooks are often preferred alternatives now).
    *   **Async/Await for Promises:** While not a design pattern in the traditional sense, mastering its use is crucial for handling asynchronous operations cleanly.
    *   *Many general patterns like Factory, Singleton, Observer, Facade, Repository are directly applicable.*
---

*(Now, detail the specific patterns chosen for THIS project and their application, referring back to the section "C. Design Patterns in Use" above.)*

### D. Component Relationships

*(Describe how the major components/modules in your proposed structure interact. This can be text or a simple diagram conceptualization, e.g., a list of components and their primary collaborators.)*

### E. Critical Implementation Paths

*(Outline the key steps or sequence of development to implement the core functionality based on the proposed architecture. For example:
1. Setup basic project structure and CI/CD.
2. Implement core domain models and business logic.
3. Develop API endpoints for key features.
4. Integrate with essential external services.
5. Write unit and integration tests for core paths.)*

---

**Final Note for the AI:**
Remember to tailor your architectural recommendations to the specifics of the PRD-plus. The goal is a practical, effective, and streamlined architecture. Avoid unnecessary complexity unless explicitly justified by the project's requirements.
