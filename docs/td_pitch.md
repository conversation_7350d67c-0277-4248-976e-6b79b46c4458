# Pitch Name
TaraDental - AI Dental Sales Training

## Problem

Dentists are never given proper sales training, and as a result there's a lot of missed opportunity to be able to properly sell patients on the care they need. Dentists are not trained on basic sales frameworks and have not practiced handling different customer personas or dealing with rejection.

## Solution

An AI patient simulation that creates and manages sessions for AI patients to simulate real-world patient objections and treatment discussions.

Objection Handling:

Uses frameworks like LAARC (Listen, Acknowledge, Assess, Respond, and Confirm) to help dental professionals address patient objections effectively.
Scripts are generated with strategic question types like clarifying and probing questions to assist dentists in guiding patients toward accepting treatments.

Coaching Flow:

Provides interactive coaching sessions that simulate patient interactions. Feedback is given based on the user's responses, aiming to improve communication strategies.
Includes steps for handling objections, educating patients about dental procedures, and confirming understanding.

Treatment Plan Presentation:

Educates patients visually using images and scripts to explain dental procedures and their importance. Examples include presenting X-ray images and discussing treatment options like crowns or implants.

Roleplay and Feedback:

Offers roleplay scenarios with AI patients, allowing dentists to practice handling objections and presenting treatments effectively.
Provides feedback on responses, guiding users with empathetic and clear communication.

Mock Data:

Contains predefined objections, scripts, and strategies for handling patient concerns, such as issues with insurance coverage, urgency, and treatment costs.

Customization and Onboarding:

Features onboarding and scenario setup for training modules. Users can reset scenarios and customize objection handling sessions.

Technical Framework:

Utilizes TypeScript and React for the client-side application and server-side scripting for generating objection handling strategies.

This product serves as an educational and coaching tool for dental professionals, leveraging AI to improve patient communication and treatment acceptance.

## Required Tools

OpenAI API for "Chat with Tara"
ElevenLabs API for roleplay situations
Supabase for DB and authentication
Vercel for web hosting
Jira for task management

## Database Structure

### Tables

CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  password_hash TEXT NOT NULL,
  first_name TEXT NOT NULL,
  last_name TEXT NOT NULL,
  display_name TEXT,
  total_xp INTEGER DEFAULT 0,
  current_streak INTEGER DEFAULT 0,
  longest_streak INTEGER DEFAULT 0,
  last_activity_date DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

CREATE TABLE daily_lessons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  how_to_apply TEXT NOT NULL,
  quiz_question TEXT NOT NULL,
  quiz_options JSONB NOT NULL, -- Array of options
  correct_answer_index INTEGER NOT NULL,
  challenge_prompt TEXT NOT NULL,
  xp_value INTEGER,
  order_index INTEGER NOT NULL,
  is_active BOOLEAN DEFAULT true,
  date TIMESTAMP,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE lesson_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  lesson_id UUID REFERENCES daily_lessons(id) ON DELETE CASCADE,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  quiz_score INTEGER, -- 0 or 1 for correct/incorrect
  challenge_response TEXT,
  xp_earned INTEGER DEFAULT 0,
  UNIQUE(user_id, lesson_id)
);

CREATE TABLE chat_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true
);

CREATE TABLE chat_messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  session_id UUID REFERENCES chat_sessions(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  message_type TEXT NOT NULL CHECK (message_type IN ('user', 'assistant')),
  content TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE patient_personas (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  personality_traits JSONB, -- Array of traits
  common_objections JSONB, -- Array of objection types
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE objection_scenarios (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  persona_id UUID REFERENCES patient_personas(id) ON DELETE CASCADE,
  scenario_context TEXT NOT NULL,
  initial_objection TEXT NOT NULL,
  objection_category TEXT NOT NULL, -- 'price', 'time', 'pain', 'trust', etc.
  difficulty_level INTEGER CHECK (difficulty_level BETWEEN 1 AND 5),
  expected_structure TEXT[], -- ['PET', 'ABC', 'LAARC']
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE roleplay_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  scenario_id UUID REFERENCES objection_scenarios(id) ON DELETE CASCADE,
  response_type TEXT NOT NULL CHECK (response_type IN ('voice', 'text')),
  user_response TEXT NOT NULL,
  ai_feedback TEXT,
  tone_score INTEGER CHECK (tone_score BETWEEN 0 AND 100),
  structure_score INTEGER CHECK (structure_score BETWEEN 0 AND 100),
  confidence_score INTEGER CHECK (confidence_score BETWEEN 0 AND 100),
  overall_score INTEGER CHECK (overall_score BETWEEN 0 AND 100),
  xp_earned INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE xp_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('lesson_quiz', 'challenge_submit', 'roleplay_complete', 'streak_bonus')),
  reference_id UUID, -- Links to lesson_progress, roleplay_session, etc.
  xp_amount INTEGER NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE user_streaks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  streak_start_date DATE NOT NULL,
  activities_completed INTEGER DEFAULT 0,
  streak_maintained BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, streak_start_date)
);


### Performance Indexes

-- User lookups
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_active ON users(is_active);

-- Lesson progress tracking
CREATE INDEX idx_user_lesson_progress_user_id ON user_lesson_progress(user_id);
CREATE INDEX idx_user_lesson_progress_completed_at ON user_lesson_progress(completed_at);

-- Chat system performance
CREATE INDEX idx_chat_sessions_user_id ON chat_sessions(user_id);
CREATE INDEX idx_chat_messages_session_id ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_created_at ON chat_messages(created_at);

-- Roleplay system
CREATE INDEX idx_roleplay_sessions_user_id ON roleplay_sessions(user_id);
CREATE INDEX idx_roleplay_sessions_completed_at ON roleplay_sessions(completed_at);

-- XP tracking
CREATE INDEX idx_xp_transactions_user_id ON xp_transactions(user_id);
CREATE INDEX idx_xp_transactions_created_at ON xp_transactions(created_at);

-- Streak tracking
CREATE INDEX idx_user_streaks_user_date ON user_streaks(user_id, streak_date);

StackEdit stores your files in your browser, which means all your files are automatically saved locally and are accessible **offline!**

### RLS Policies

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_lesson_progress ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE roleplay_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE xp_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_streaks ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own lesson progress" ON user_lesson_progress FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own chat sessions" ON chat_sessions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own chat messages" ON chat_messages FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own roleplay sessions" ON roleplay_sessions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own XP transactions" ON xp_transactions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own streaks" ON user_streaks FOR ALL USING (auth.uid() = user_id);

-- Public read access for lesson content and personas
CREATE POLICY "Anyone can view active lessons" ON daily_lessons FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view active personas" ON patient_personas FOR SELECT USING (is_active = true);
CREATE POLICY "Anyone can view active scenarios" ON objection_scenarios FOR SELECT USING (is_active = true);


## Main components

### Daily Lessons

users table tracks accounts with XP/streak data
daily_lessons and user_lesson_progress handle the Read → Apply → Quiz → Challenge flow
xp_transactions logs all XP awards with detailed tracking

### Objection Handling + Grading

patient_personas and objection_scenarios create realistic roleplay situations
roleplay_sessions capture responses and detailed scoring (tone, structure, confidence)
Supports both voice and text response tracking

### Chat with Tara

chat_sessions and chat_messages provide session-based chat history
User names stored for personalized interactions
Message types distinguish user vs. AI responses

### Gamification Layer

user_streaks handles daily activity and streak reset logic
xp_transactions provides audit trail for all XP events
Multiple XP earning opportunities tracked with reference IDs