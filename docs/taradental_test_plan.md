# TaraDental - Comprehensive Test Plan & Code Generation

**Note:** This test plan is designed for implementation once the corresponding tasks are completed. Currently, all tasks in the project plan are marked as incomplete `[ ]`. This document provides the testing strategy and code templates for when features are implemented.

---

## Test Suite for: Authentication System

**A. Relevant Task IDs (when completed):**
*   Task 1.1: Implement Supabase Auth Integration
*   Task 1.2: Backend Authentication Middleware

**B. Brief Test Strategy:**
*   **Unit Tests:**
    *   Target File(s): `frontend/src/hooks/useAuth.ts`, `backend/src/middleware/auth.ts`
    *   Focus: Test authentication hook state management, JWT validation logic, token extraction
    *   Mocks: Mock Supabase client responses for predictable testing
*   **Integration Tests:**
    *   Target Endpoint(s): `POST /api/auth/login`, `POST /api/auth/signup`, protected endpoints
    *   Focus: Verify end-to-end authentication flow from frontend to backend
    *   Mocks: Use test Supabase project or mock Supabase responses
*   **Behavioral/E2E Test Scenarios:**
    1. User successfully signs up with valid credentials and receives confirmation
    2. User logs in with valid credentials and accesses protected routes
    3. User attempts to access protected routes without authentication and is redirected
    4. User logs out and loses access to protected content

**C. Generated Test Code:**

### Unit Tests

**TypeScript Example: File:** `frontend/src/tests/unit/useAuth.test.ts`

```typescript
import { renderHook, act } from '@testing-library/react';
import { useAuth } from '../../hooks/useAuth';
import { createClient } from '@supabase/supabase-js';

// Mock Supabase
jest.mock('@supabase/supabase-js');
const mockSupabase = createClient as jest.MockedFunction<typeof createClient>;

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should initialize with null user', () => {
    const { result } = renderHook(() => useAuth());
    expect(result.current.user).toBeNull();
    expect(result.current.loading).toBe(false);
  });

  it('should handle successful login', async () => {
    const mockUser = { id: '123', email: '<EMAIL>' };
    mockSupabase.mockReturnValue({
      auth: {
        signInWithPassword: jest.fn().mockResolvedValue({
          data: { user: mockUser },
          error: null
        })
      }
    } as any);

    const { result } = renderHook(() => useAuth());
    
    await act(async () => {
      await result.current.login('<EMAIL>', 'password');
    });

    expect(result.current.user).toEqual(mockUser);
    expect(result.current.error).toBeNull();
  });

  it('should handle login error', async () => {
    const mockError = { message: 'Invalid credentials' };
    mockSupabase.mockReturnValue({
      auth: {
        signInWithPassword: jest.fn().mockResolvedValue({
          data: { user: null },
          error: mockError
        })
      }
    } as any);

    const { result } = renderHook(() => useAuth());
    
    await act(async () => {
      await result.current.login('<EMAIL>', 'wrongpassword');
    });

    expect(result.current.user).toBeNull();
    expect(result.current.error).toEqual(mockError);
  });
});
```

**TypeScript Example: File:** `backend/src/tests/unit/auth.middleware.test.ts`

```typescript
import { Request, Response, NextFunction } from 'express';
import { authMiddleware } from '../../middleware/auth';
import jwt from 'jsonwebtoken';

jest.mock('jsonwebtoken');
const mockJwt = jwt as jest.Mocked<typeof jwt>;

describe('Auth Middleware', () => {
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;
  let mockNext: NextFunction;

  beforeEach(() => {
    mockRequest = {
      headers: {}
    };
    mockResponse = {
      status: jest.fn().mockReturnThis(),
      json: jest.fn()
    };
    mockNext = jest.fn();
  });

  it('should pass valid JWT token', async () => {
    const mockPayload = { sub: 'user123', email: '<EMAIL>' };
    mockRequest.headers = { authorization: 'Bearer valid-token' };
    mockJwt.verify.mockReturnValue(mockPayload as any);

    await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockRequest.user).toEqual(mockPayload);
    expect(mockNext).toHaveBeenCalled();
  });

  it('should reject request without authorization header', async () => {
    await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({ error: 'No token provided' });
    expect(mockNext).not.toHaveBeenCalled();
  });

  it('should reject invalid JWT token', async () => {
    mockRequest.headers = { authorization: 'Bearer invalid-token' };
    mockJwt.verify.mockImplementation(() => {
      throw new Error('Invalid token');
    });

    await authMiddleware(mockRequest as Request, mockResponse as Response, mockNext);

    expect(mockResponse.status).toHaveBeenCalledWith(401);
    expect(mockResponse.json).toHaveBeenCalledWith({ error: 'Invalid token' });
    expect(mockNext).not.toHaveBeenCalled();
  });
});
```

### Integration Tests

**TypeScript Example: File:** `backend/src/tests/integration/auth.integration.test.ts`

```typescript
import request from 'supertest';
import { app } from '../../app';
import { createClient } from '@supabase/supabase-js';

describe('Authentication Integration', () => {
  describe('POST /api/auth/login', () => {
    it('should authenticate user with valid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'validpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('user');
      expect(response.body).toHaveProperty('session');
      expect(response.body.user.email).toBe(loginData.email);
    });

    it('should reject invalid credentials', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword'
      };

      const response = await request(app)
        .post('/api/auth/login')
        .send(loginData);

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('error');
    });
  });

  describe('Protected Routes', () => {
    it('should allow access with valid token', async () => {
      // First, get a valid token
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({ email: '<EMAIL>', password: 'validpassword' });

      const token = loginResponse.body.session.access_token;

      const response = await request(app)
        .get('/api/user/profile')
        .set('Authorization', `Bearer ${token}`);

      expect(response.status).toBe(200);
    });

    it('should deny access without token', async () => {
      const response = await request(app)
        .get('/api/user/profile');

      expect(response.status).toBe(401);
    });
  });
});
```

---

## Test Suite for: Daily Lessons System

**A. Relevant Task IDs (when completed):**
*   Task 2.1: Lesson Content Management
*   Task 2.2: Frontend Lesson Components
*   Task 2.3: XP & Gamification System

**B. Brief Test Strategy:**
*   **Unit Tests:**
    *   Target File(s): `backend/src/services/lessonService.ts`, `frontend/src/components/lessons/LessonCard.tsx`
    *   Focus: Test lesson CRUD operations, XP calculation logic, component rendering
    *   Mocks: Mock database operations, mock lesson data
*   **Integration Tests:**
    *   Target Endpoint(s): `GET /api/lessons`, `POST /api/lessons/:id/complete`, `GET /api/user/progress`
    *   Focus: Verify lesson completion flow, XP tracking, progress updates
    *   Mocks: Use test database with seeded lesson data
*   **Behavioral/E2E Test Scenarios:**
    1. User completes a lesson and earns XP
    2. User maintains daily streak through consecutive lesson completion
    3. User views progress dashboard with accurate statistics
    4. User accesses lesson content in correct sequence

**C. Generated Test Code:**

### Unit Tests

**TypeScript Example: File:** `backend/src/tests/unit/lessonService.test.ts`

```typescript
import { LessonService } from '../../services/lessonService';
import { ProgressService } from '../../services/progressService';

jest.mock('../../services/progressService');
const mockProgressService = ProgressService as jest.MockedClass<typeof ProgressService>;

describe('LessonService', () => {
  let lessonService: LessonService;
  let mockSupabaseClient: any;

  beforeEach(() => {
    mockSupabaseClient = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn()
    };
    lessonService = new LessonService(mockSupabaseClient);
  });

  describe('completeLesson', () => {
    it('should mark lesson as complete and award XP', async () => {
      const userId = 'user123';
      const lessonId = 'lesson456';
      const mockLesson = { id: lessonId, xp_value: 50 };

      mockSupabaseClient.single.mockResolvedValue({
        data: mockLesson,
        error: null
      });

      const result = await lessonService.completeLesson(userId, lessonId, 1, 'Challenge response');

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('lesson_progress');
      expect(mockSupabaseClient.insert).toHaveBeenCalledWith({
        user_id: userId,
        lesson_id: lessonId,
        quiz_score: 1,
        challenge_response: 'Challenge response',
        xp_earned: 50
      });
    });

    it('should handle lesson completion errors', async () => {
      const userId = 'user123';
      const lessonId = 'lesson456';

      mockSupabaseClient.single.mockResolvedValue({
        data: null,
        error: { message: 'Lesson not found' }
      });

      await expect(lessonService.completeLesson(userId, lessonId, 1, 'response'))
        .rejects.toThrow('Lesson not found');
    });
  });

  describe('calculateXP', () => {
    it('should calculate correct XP for perfect quiz score', () => {
      const baseXP = 50;
      const quizScore = 1;
      const streakMultiplier = 1.2;

      const result = lessonService.calculateXP(baseXP, quizScore, streakMultiplier);

      expect(result).toBe(60); // 50 * 1 * 1.2
    });

    it('should apply streak bonus correctly', () => {
      const baseXP = 100;
      const quizScore = 1;
      const streakMultiplier = 1.5;

      const result = lessonService.calculateXP(baseXP, quizScore, streakMultiplier);

      expect(result).toBe(150);
    });
  });
});
```

**TypeScript Example: File:** `frontend/src/tests/unit/LessonCard.test.tsx`

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { LessonCard } from '../../components/lessons/LessonCard';
import { Lesson } from '../../../shared/types/lesson';

const mockLesson: Lesson = {
  id: '1',
  title: 'Introduction to LAARC',
  content: 'Learn the LAARC framework',
  how_to_apply: 'Apply in patient interactions',
  quiz_question: 'What does LAARC stand for?',
  quiz_options: ['Listen, Acknowledge, Assess, Respond, Confirm', 'Other option'],
  correct_answer_index: 0,
  challenge_prompt: 'Practice with a scenario',
  xp_value: 50,
  order_index: 1,
  is_active: true
};

describe('LessonCard', () => {
  it('should render lesson information correctly', () => {
    render(<LessonCard lesson={mockLesson} onComplete={jest.fn()} />);

    expect(screen.getByText('Introduction to LAARC')).toBeInTheDocument();
    expect(screen.getByText('Learn the LAARC framework')).toBeInTheDocument();
    expect(screen.getByText('50 XP')).toBeInTheDocument();
  });

  it('should handle quiz submission', () => {
    const mockOnComplete = jest.fn();
    render(<LessonCard lesson={mockLesson} onComplete={mockOnComplete} />);

    // Select correct answer
    const correctOption = screen.getByText('Listen, Acknowledge, Assess, Respond, Confirm');
    fireEvent.click(correctOption);

    // Submit quiz
    const submitButton = screen.getByText('Submit Quiz');
    fireEvent.click(submitButton);

    expect(mockOnComplete).toHaveBeenCalledWith({
      lessonId: '1',
      quizScore: 1,
      challengeResponse: expect.any(String)
    });
  });

  it('should show correct feedback for wrong answer', () => {
    render(<LessonCard lesson={mockLesson} onComplete={jest.fn()} />);

    // Select wrong answer
    const wrongOption = screen.getByText('Other option');
    fireEvent.click(wrongOption);

    const submitButton = screen.getByText('Submit Quiz');
    fireEvent.click(submitButton);

    expect(screen.getByText(/incorrect/i)).toBeInTheDocument();
  });
});
```

### Integration Tests

**TypeScript Example: File:** `backend/src/tests/integration/lessons.integration.test.ts`

```typescript
import request from 'supertest';
import { app } from '../../app';

describe('Lessons API Integration', () => {
  let authToken: string;

  beforeAll(async () => {
    // Get auth token for testing
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });
    authToken = loginResponse.body.session.access_token;
  });

  describe('GET /api/lessons', () => {
    it('should return available lessons for authenticated user', async () => {
      const response = await request(app)
        .get('/api/lessons')
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('lessons');
      expect(Array.isArray(response.body.lessons)).toBe(true);
    });
  });

  describe('POST /api/lessons/:id/complete', () => {
    it('should complete lesson and award XP', async () => {
      const lessonId = 'test-lesson-1';
      const completionData = {
        quiz_score: 1,
        challenge_response: 'I would use LAARC to handle the objection'
      };

      const response = await request(app)
        .post(`/api/lessons/${lessonId}/complete`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(completionData);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('xp_earned');
      expect(response.body.xp_earned).toBeGreaterThan(0);
    });

    it('should prevent duplicate lesson completion', async () => {
      const lessonId = 'test-lesson-1';
      const completionData = {
        quiz_score: 1,
        challenge_response: 'Second attempt'
      };

      const response = await request(app)
        .post(`/api/lessons/${lessonId}/complete`)
        .set('Authorization', `Bearer ${authToken}`)
        .send(completionData);

      expect(response.status).toBe(409);
      expect(response.body).toHaveProperty('error');
    });
  });
});
```

---

## Test Suite for: AI Chat & Roleplay System

**A. Relevant Task IDs (when completed):**
*   Task 3.1: OpenAI Integration
*   Task 3.2: Chat Interface Components
*   Task 3.3: Patient Persona Management
*   Task 3.4: Roleplay Interface Components

**B. Brief Test Strategy:**
*   **Unit Tests:**
    *   Target File(s): `backend/src/services/aiService.ts`, `backend/src/services/roleplayService.ts`
    *   Focus: Test AI response generation, persona selection logic, conversation context management
    *   Mocks: Mock OpenAI API responses for predictable testing
*   **Integration Tests:**
    *   Target Endpoint(s): `POST /api/chat/message`, `POST /api/roleplay/start`, `POST /api/roleplay/respond`
    *   Focus: Verify AI integration, roleplay session management, response scoring
    *   Mocks: Mock AI services with predefined responses
*   **Behavioral/E2E Test Scenarios:**
    1. User starts chat with Tara and receives contextual responses
    2. User begins roleplay session with AI patient persona
    3. User practices objection handling and receives LAARC framework feedback
    4. User completes roleplay session and views performance scores

**C. Generated Test Code:**

### Unit Tests

**TypeScript Example: File:** `backend/src/tests/unit/aiService.test.ts`

```typescript
import { AIService } from '../../services/aiService';
import OpenAI from 'openai';

jest.mock('openai');
const MockOpenAI = OpenAI as jest.MockedClass<typeof OpenAI>;

describe('AIService', () => {
  let aiService: AIService;
  let mockOpenAI: jest.Mocked<OpenAI>;

  beforeEach(() => {
    mockOpenAI = new MockOpenAI() as jest.Mocked<OpenAI>;
    aiService = new AIService(mockOpenAI);
  });

  describe('generateChatResponse', () => {
    it('should generate appropriate response for dental sales question', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'To handle price objections, use the LAARC framework...'
          }
        }]
      };

      mockOpenAI.chat.completions.create = jest.fn().mockResolvedValue(mockResponse);

      const result = await aiService.generateChatResponse(
        'How do I handle price objections?',
        'dental_sales_coach'
      );

      expect(result).toBe('To handle price objections, use the LAARC framework...');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith({
        model: 'gpt-4',
        messages: expect.arrayContaining([
          expect.objectContaining({
            role: 'system',
            content: expect.stringContaining('dental sales coach')
          }),
          expect.objectContaining({
            role: 'user',
            content: 'How do I handle price objections?'
          })
        ]),
        temperature: 0.7,
        max_tokens: 500
      });
    });

    it('should handle OpenAI API errors gracefully', async () => {
      mockOpenAI.chat.completions.create = jest.fn().mockRejectedValue(
        new Error('API rate limit exceeded')
      );

      await expect(aiService.generateChatResponse('test question', 'coach'))
        .rejects.toThrow('AI service temporarily unavailable');
    });
  });

  describe('generatePatientResponse', () => {
    it('should generate persona-appropriate response', async () => {
      const mockPersona = {
        id: '1',
        name: 'Anxious Annie',
        personality_traits: ['anxious', 'cost-conscious'],
        common_objections: ['price', 'pain']
      };

      const mockResponse = {
        choices: [{
          message: {
            content: 'I\'m really worried about the cost and if it will hurt...'
          }
        }]
      };

      mockOpenAI.chat.completions.create = jest.fn().mockResolvedValue(mockResponse);

      const result = await aiService.generatePatientResponse(
        'We recommend a crown for your tooth',
        mockPersona,
        'treatment_recommendation'
      );

      expect(result).toContain('worried about the cost');
      expect(mockOpenAI.chat.completions.create).toHaveBeenCalledWith(
        expect.objectContaining({
          messages: expect.arrayContaining([
            expect.objectContaining({
              content: expect.stringContaining('anxious')
            })
          ])
        })
      );
    });
  });
});
```

**TypeScript Example: File:** `backend/src/tests/unit/roleplayService.test.ts`

```typescript
import { RoleplayService } from '../../services/roleplayService';
import { AIService } from '../../services/aiService';

jest.mock('../../services/aiService');
const MockAIService = AIService as jest.MockedClass<typeof AIService>;

describe('RoleplayService', () => {
  let roleplayService: RoleplayService;
  let mockAIService: jest.Mocked<AIService>;
  let mockSupabaseClient: any;

  beforeEach(() => {
    mockAIService = new MockAIService() as jest.Mocked<AIService>;
    mockSupabaseClient = {
      from: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      single: jest.fn()
    };
    roleplayService = new RoleplayService(mockSupabaseClient, mockAIService);
  });

  describe('startRoleplaySession', () => {
    it('should create new roleplay session with selected persona', async () => {
      const userId = 'user123';
      const scenarioId = 'scenario456';
      const mockScenario = {
        id: scenarioId,
        persona_id: 'persona789',
        scenario_context: 'Patient needs crown',
        initial_objection: 'Too expensive'
      };

      mockSupabaseClient.single.mockResolvedValue({
        data: mockScenario,
        error: null
      });

      const result = await roleplayService.startRoleplaySession(userId, scenarioId);

      expect(mockSupabaseClient.from).toHaveBeenCalledWith('objection_scenarios');
      expect(result).toHaveProperty('sessionId');
      expect(result).toHaveProperty('initialMessage');
    });
  });

  describe('scoreResponse', () => {
    it('should score LAARC framework usage correctly', () => {
      const userResponse = 'I understand your concern about cost. Let me assess your specific needs and explain the value.';
      const expectedFramework = ['LAARC'];

      const scores = roleplayService.scoreResponse(userResponse, expectedFramework);

      expect(scores.structure_score).toBeGreaterThan(70);
      expect(scores.tone_score).toBeGreaterThan(60);
      expect(scores.confidence_score).toBeGreaterThan(50);
    });

    it('should penalize responses missing LAARC elements', () => {
      const userResponse = 'It\'s not that expensive.';
      const expectedFramework = ['LAARC'];

      const scores = roleplayService.scoreResponse(userResponse, expectedFramework);

      expect(scores.structure_score).toBeLessThan(50);
      expect(scores.overall_score).toBeLessThan(60);
    });
  });
});
```

### Integration Tests

**TypeScript Example: File:** `backend/src/tests/integration/roleplay.integration.test.ts`

```typescript
import request from 'supertest';
import { app } from '../../app';

describe('Roleplay API Integration', () => {
  let authToken: string;

  beforeAll(async () => {
    const loginResponse = await request(app)
      .post('/api/auth/login')
      .send({ email: '<EMAIL>', password: 'password' });
    authToken = loginResponse.body.session.access_token;
  });

  describe('POST /api/roleplay/start', () => {
    it('should start new roleplay session', async () => {
      const response = await request(app)
        .post('/api/roleplay/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ scenario_id: 'test-scenario-1' });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('sessionId');
      expect(response.body).toHaveProperty('initialMessage');
      expect(response.body).toHaveProperty('persona');
    });
  });

  describe('POST /api/roleplay/respond', () => {
    it('should process user response and return AI feedback', async () => {
      // First start a session
      const startResponse = await request(app)
        .post('/api/roleplay/start')
        .set('Authorization', `Bearer ${authToken}`)
        .send({ scenario_id: 'test-scenario-1' });

      const sessionId = startResponse.body.sessionId;

      const response = await request(app)
        .post('/api/roleplay/respond')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          session_id: sessionId,
          user_response: 'I understand your concern about the cost. Let me explain the value.',
          response_type: 'text'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('ai_response');
      expect(response.body).toHaveProperty('scores');
      expect(response.body.scores).toHaveProperty('tone_score');
      expect(response.body.scores).toHaveProperty('structure_score');
      expect(response.body.scores).toHaveProperty('confidence_score');
    });
  });
});
```

---

## Test Suite for: Voice Integration System

**A. Relevant Task IDs (when completed):**
*   Task 3.5: ElevenLabs Voice Synthesis
*   Task 3.6: Voice Recording Components

**B. Brief Test Strategy:**
*   **Unit Tests:**
    *   Target File(s): `backend/src/services/voiceService.ts`, `frontend/src/components/roleplay/VoiceRecorder.tsx`
    *   Focus: Test voice synthesis API integration, audio recording functionality, file upload handling
    *   Mocks: Mock ElevenLabs API responses, mock browser MediaRecorder API
*   **Integration Tests:**
    *   Target Endpoint(s): `POST /api/voice/synthesize`, `POST /api/voice/upload`
    *   Focus: Verify voice generation, audio file processing, streaming capabilities
    *   Mocks: Mock ElevenLabs service with audio blob responses
*   **Behavioral/E2E Test Scenarios:**
    1. User records voice response during roleplay session
    2. AI patient responds with synthesized voice
    3. User plays back their recorded response
    4. Voice responses are properly cached for performance

**C. Generated Test Code:**

### Unit Tests

**TypeScript Example: File:** `backend/src/tests/unit/voiceService.test.ts`

```typescript
import { VoiceService } from '../../services/voiceService';
import { ElevenLabsApi } from 'elevenlabs-node';

jest.mock('elevenlabs-node');
const MockElevenLabs = ElevenLabsApi as jest.MockedClass<typeof ElevenLabsApi>;

describe('VoiceService', () => {
  let voiceService: VoiceService;
  let mockElevenLabs: jest.Mocked<ElevenLabsApi>;

  beforeEach(() => {
    mockElevenLabs = new MockElevenLabs() as jest.Mocked<ElevenLabsApi>;
    voiceService = new VoiceService(mockElevenLabs);
  });

  describe('synthesizeVoice', () => {
    it('should generate audio from text with specified voice', async () => {
      const mockAudioBuffer = Buffer.from('fake-audio-data');
      mockElevenLabs.textToSpeech = jest.fn().mockResolvedValue(mockAudioBuffer);

      const result = await voiceService.synthesizeVoice(
        'Hello, I understand your concern about the treatment cost.',
        'patient-voice-1'
      );

      expect(result).toEqual(mockAudioBuffer);
      expect(mockElevenLabs.textToSpeech).toHaveBeenCalledWith(
        'patient-voice-1',
        'Hello, I understand your concern about the treatment cost.',
        expect.any(Object)
      );
    });

    it('should handle voice synthesis errors', async () => {
      mockElevenLabs.textToSpeech = jest.fn().mockRejectedValue(
        new Error('Voice synthesis failed')
      );

      await expect(voiceService.synthesizeVoice('test text', 'voice-id'))
        .rejects.toThrow('Voice synthesis service unavailable');
    });
  });

  describe('cacheVoiceResponse', () => {
    it('should cache generated audio with proper key', async () => {
      const mockAudioBuffer = Buffer.from('audio-data');
      const textHash = 'hash123';
      const voiceId = 'voice-1';

      await voiceService.cacheVoiceResponse(textHash, voiceId, mockAudioBuffer);

      // Verify caching logic (implementation depends on cache service)
      expect(voiceService.getCachedVoice(textHash, voiceId)).resolves.toEqual(mockAudioBuffer);
    });
  });
});
```

**TypeScript Example: File:** `frontend/src/tests/unit/VoiceRecorder.test.tsx`

```typescript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { VoiceRecorder } from '../../components/roleplay/VoiceRecorder';

// Mock MediaRecorder
const mockMediaRecorder = {
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  state: 'inactive'
};

Object.defineProperty(window, 'MediaRecorder', {
  writable: true,
  value: jest.fn().mockImplementation(() => mockMediaRecorder)
});

Object.defineProperty(navigator, 'mediaDevices', {
  writable: true,
  value: {
    getUserMedia: jest.fn().mockResolvedValue({
      getTracks: () => [{ stop: jest.fn() }]
    })
  }
});

describe('VoiceRecorder', () => {
  const mockOnRecordingComplete = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render recording controls', () => {
    render(<VoiceRecorder onRecordingComplete={mockOnRecordingComplete} />);

    expect(screen.getByText('Start Recording')).toBeInTheDocument();
    expect(screen.getByText('Record your response')).toBeInTheDocument();
  });

  it('should start recording when button is clicked', async () => {
    render(<VoiceRecorder onRecordingComplete={mockOnRecordingComplete} />);

    const startButton = screen.getByText('Start Recording');
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
        audio: true
      });
    });

    expect(mockMediaRecorder.start).toHaveBeenCalled();
    expect(screen.getByText('Stop Recording')).toBeInTheDocument();
  });

  it('should stop recording and process audio', async () => {
    render(<VoiceRecorder onRecordingComplete={mockOnRecordingComplete} />);

    // Start recording first
    const startButton = screen.getByText('Start Recording');
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText('Stop Recording')).toBeInTheDocument();
    });

    // Stop recording
    const stopButton = screen.getByText('Stop Recording');
    fireEvent.click(stopButton);

    expect(mockMediaRecorder.stop).toHaveBeenCalled();
  });

  it('should handle recording errors gracefully', async () => {
    (navigator.mediaDevices.getUserMedia as jest.Mock).mockRejectedValue(
      new Error('Microphone access denied')
    );

    render(<VoiceRecorder onRecordingComplete={mockOnRecordingComplete} />);

    const startButton = screen.getByText('Start Recording');
    fireEvent.click(startButton);

    await waitFor(() => {
      expect(screen.getByText(/microphone access/i)).toBeInTheDocument();
    });
  });
});
```

---

## Test Suite for: End-to-End User Flows

**A. Relevant Task IDs (when completed):**
*   All tasks from Phases 1-3 (Authentication, Lessons, AI Integration)

**B. Brief Test Strategy:**
*   **E2E Tests:**
    *   Target Flow(s): Complete user journey from signup to lesson completion to roleplay
    *   Focus: Critical user paths, cross-component integration, real browser interactions
    *   Tools: Playwright for browser automation
*   **Performance Tests:**
    *   Target: AI response times, page load speeds, audio processing
    *   Focus: Meet 3-second AI response requirement, smooth user experience

**C. Generated Test Code:**

### End-to-End Tests

**TypeScript Example: File:** `tests/e2e/user-journey.spec.ts`

```typescript
import { test, expect } from '@playwright/test';

test.describe('TaraDental User Journey', () => {
  test('complete user onboarding and first lesson', async ({ page }) => {
    // Navigate to application
    await page.goto('/');

    // Sign up new user
    await page.click('text=Sign Up');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'securepassword');
    await page.click('[data-testid=signup-button]');

    // Verify successful signup and redirect to dashboard
    await expect(page).toHaveURL('/dashboard');
    await expect(page.locator('text=Welcome to TaraDental')).toBeVisible();

    // Navigate to lessons
    await page.click('text=Daily Lessons');
    await expect(page).toHaveURL('/lessons');

    // Complete first lesson
    await page.click('[data-testid=lesson-card-1]');

    // Read lesson content
    await expect(page.locator('text=Introduction to LAARC')).toBeVisible();
    await page.click('text=Continue to Quiz');

    // Answer quiz question
    await page.click('[data-testid=quiz-option-0]'); // Correct answer
    await page.click('text=Submit Quiz');

    // Complete challenge
    await page.fill('[data-testid=challenge-response]',
      'I would use LAARC by first listening to the patient\'s concern...');
    await page.click('text=Submit Challenge');

    // Verify XP award
    await expect(page.locator('text=+50 XP')).toBeVisible();
    await expect(page.locator('text=Lesson Complete!')).toBeVisible();

    // Return to dashboard and verify progress
    await page.click('text=Dashboard');
    await expect(page.locator('[data-testid=total-xp]')).toContainText('50');
    await expect(page.locator('[data-testid=current-streak]')).toContainText('1');
  });

  test('complete AI roleplay session', async ({ page }) => {
    // Login as existing user
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password');
    await page.click('[data-testid=login-button]');

    // Navigate to roleplay
    await page.click('text=Practice Roleplay');
    await expect(page).toHaveURL('/roleplay');

    // Start new roleplay session
    await page.click('text=Start New Session');
    await page.selectOption('[data-testid=persona-select]', 'anxious-annie');
    await page.click('text=Begin Roleplay');

    // Verify AI patient introduction
    await expect(page.locator('[data-testid=ai-message]')).toBeVisible();
    await expect(page.locator('text=I\'m worried about')).toBeVisible();

    // Respond to AI patient
    await page.fill('[data-testid=user-response]',
      'I understand your concern. Let me acknowledge that dental procedures can feel overwhelming...');
    await page.click('text=Send Response');

    // Wait for AI response and feedback
    await expect(page.locator('[data-testid=ai-response]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('[data-testid=feedback-scores]')).toBeVisible();

    // Verify LAARC framework feedback
    await expect(page.locator('text=Structure Score')).toBeVisible();
    await expect(page.locator('text=Tone Score')).toBeVisible();
    await expect(page.locator('text=Confidence Score')).toBeVisible();

    // Complete session
    await page.click('text=End Session');
    await expect(page.locator('text=Session Complete')).toBeVisible();
    await expect(page.locator('[data-testid=session-summary]')).toBeVisible();
  });

  test('chat with Tara functionality', async ({ page }) => {
    // Login and navigate to chat
    await page.goto('/login');
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password');
    await page.click('[data-testid=login-button]');

    await page.click('text=Chat with Tara');
    await expect(page).toHaveURL('/chat');

    // Send message to Tara
    await page.fill('[data-testid=chat-input]',
      'How do I handle a patient who says the treatment is too expensive?');
    await page.click('[data-testid=send-button]');

    // Verify message appears in chat
    await expect(page.locator('[data-testid=user-message]')).toContainText('too expensive');

    // Wait for Tara's response
    await expect(page.locator('[data-testid=tara-response]')).toBeVisible({ timeout: 5000 });
    await expect(page.locator('text=LAARC framework')).toBeVisible();

    // Verify chat history is maintained
    await page.reload();
    await expect(page.locator('[data-testid=user-message]')).toContainText('too expensive');
    await expect(page.locator('[data-testid=tara-response]')).toBeVisible();
  });
});

test.describe('Performance Tests', () => {
  test('AI response time meets requirements', async ({ page }) => {
    await page.goto('/chat');

    // Login first
    await page.fill('[data-testid=email-input]', '<EMAIL>');
    await page.fill('[data-testid=password-input]', 'password');
    await page.click('[data-testid=login-button]');

    // Measure AI response time
    const startTime = Date.now();

    await page.fill('[data-testid=chat-input]', 'Test question for response time');
    await page.click('[data-testid=send-button]');

    await page.waitForSelector('[data-testid=tara-response]', { timeout: 5000 });

    const responseTime = Date.now() - startTime;

    // Verify response time is under 3 seconds (3000ms)
    expect(responseTime).toBeLessThan(3000);
  });
});
```

---

## Summary

This comprehensive test plan covers all critical functionality of the TaraDental platform:

1. **Authentication System** - Login, signup, JWT validation, protected routes
2. **Daily Lessons System** - Lesson completion, XP tracking, gamification
3. **AI Chat & Roleplay** - OpenAI integration, persona management, LAARC scoring
4. **Voice Integration** - ElevenLabs synthesis, audio recording, caching
5. **End-to-End Flows** - Complete user journeys, performance validation

**Key Testing Principles Applied:**
- Comprehensive unit test coverage for business logic
- Integration tests for API endpoints and service interactions
- E2E tests for critical user flows
- Performance tests for AI response time requirements
- Proper mocking strategies for external dependencies
- Clear test organization and maintainable code structure

**Implementation Notes:**
- All tests are designed to run once the corresponding tasks are completed
- Mock strategies minimize external dependencies while maintaining realistic test scenarios
- Performance tests ensure the 3-second AI response requirement is met
- E2E tests validate complete user workflows across the entire application
```
```
